<?php
    ini_set("display_errors", "1");
    error_reporting(E_ALL);
    
    require_once "InvoiceController.php";
    require_once CONN;

    // Request Parameters
    $shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : 
                (isset($_COOKIE['shopid']) ? filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING) : '6062');

    $roid = isset($_REQUEST['roid']) ? filter_var($_REQUEST['roid'], FILTER_VALIDATE_INT) : 1001;
    $remoteauth = isset($_REQUEST['remoteauth']) ? filter_var($_REQUEST['remoteauth'], FILTER_SANITIZE_STRING) : '';
    $showSeparateCanadianTaxes = false;
    $showVehicleColor = false;

    // Data
    $invoiceController = new InvoiceController($shopid, $roid);
    
    // Invoice Preferences
    $invoice_statement="SELECT *
                    FROM company_invoice_settings 
                    WHERE shopid = ?";
    $invoice = $invoiceController->query(
        $invoice_statement,
        [
            ['s', $shopid]
        ]
    );

    $printroid = $roid;

    // Company
    $company = $invoiceController->query(
        "SELECT CompanyName, nexpartusername, CompanyState, hst, pst, gst, qst, chargehst, chargepst, chargegst, chargeqst, invoicedimension, showdeclined, showrevapps, showinvoicenumber, showtechoninvoice, printbar, printvitotals, CompanyName, CompanyAddress, CompanyCity, CompanyState, CompanyZip, CompanyPhone, CompanyFax, CompanyEmail, printcommlog, CompanyURL, printpopup, StorageFee, showpartnumberonprintedro, EPANo, BarNo, rowarrdisclosure, rodisclosure, showpayments, printpayments, printtechstory, printadvisorcomments, showlaborhoursonro, showlaborratesonro, milesinlabel, milesoutlabel, replacerowithtag, logo, invoicetitle, estimatetitle, itemizefeesprintedro, showtirepressure, partsdiscountonro, showsourceonprintedro, showpcodeoninvoice, showadvisoroninvoice,matco  FROM company WHERE shopid = ?",
        [
            ['s', $shopid]
        ]
    );

    // Repair Order
    $ro = $invoiceController->query(
        "SELECT roid, cb, canadiantax, customerid, `status`, COALESCE(UCASE(tagnumber),'') AS tagnumber, Writer, `source`, warrmos, warrmiles, email, rev1amt, rev1date, rev1phone, rev1time, rev1by, rev2amt, rev2date, rev2phone, rev2time, rev2by, tirepressureinlf, tirepressureinrf, tirepressureinlr, tirepressureinrr, tirepressureoutlf, tirepressureoutlr, tirepressureoutrf, tirepressureoutrr, treaddepthlf, treaddepthlr, treaddepthrf, treaddepthrr, rodisc, warrdisc, ponumber, vehinfo, UCASE(vehlicense) as vehlicense, vehstate, vehiclemiles, milesout, vehengine, vin, fleetno, customvehicle1label, customvehicle1, customvehicle2label, customvehicle2, customvehicle3label, customvehicle3, customvehicle4label, customvehicle4, discountamt, `status`, userfee1, userfee1label, userfee2, userfee2label, userfee3, userfee3label, HazardousWaste, storagefee, vehmake, vehmodel, VehTrans, DriveType, Customer, CustomerAddress, DateIn, customercity, customerstate, customerzip, CustomerPhone, CellPhone, CustomerWork, vehstate, StatusDate, vehyear, VehicleMiles, MilesOut, TotalRO, origro, TotalPrts, TotalLbr, TotalSublet, TotalFees, Subtotal, TotalFees, TaxRate, SalesTax, AmtPaid1, AmtPaid2, vehid, recall, datetimepromised FROM repairorders WHERE shopid = ? AND roid = ?", 
        [
            ['s', $shopid],
            ['i', $roid]
        ]
    );

    // Vehicle Color
    if ($showVehicleColor) {
        $vehicleColor = ($invoiceController->query(
            "SELECT UCASE(color) as color FROM vehicles WHERE shopid = ? AND VehID = ?",
            [
                ['s', $shopid],
                ['i', $veh_id]
            ]
        ))['color'];
    }

    // Tag Number
        $printroid = strtolower($company["replacerowithtag"]) == "yes" && !empty($tagnum)
            ? $ro["tagnumber"] 
            : $roid;

    // Company Address
        $shopAddress = [];

        if (!empty($company["CompanyCity"])) {
            $shopAddress[] = $company["CompanyCity"];
        }

        if (!empty($company["CompanyState"])) {
            $shopAddress[] = $company["CompanyState"];
        }

        if (!empty($company["CompanyZip"])) {
            $shopAddress[] = $company["CompanyZip"];
        }

        $shopcsz = implode(', ', $shopAddress);
    // END Company Address

    // Determine logo path and URL
        $logoURL = "";
        $logoPath = "";
        $hasLogo = true;
        $logo = $company['logo'];
        

        if (!empty($logo)) {
            // Custom logo from upload
            $logoPath = "\\fs.shopboss.aws\share\upload\\$shopid\\$logo";
            $logoURL = "https://" . $_SERVER['SERVER_NAME'] . "/sbp/upload/$shopid/$logo";
        } else {
            $hasLogo = false;
            // Default logos based on document status and type
            if ($ro['status'] == "FINAL" || $ro['status'] == "CLOSED") {
                if (strlen($company['invoicetitle']) > 1) {
                    $logoPath = IMAGE . "/newimages/invoicelogo.png";
                    $logoURL = $logoPath;
                }
            } else {
                if (strlen($company['estimatetitle']) > 1) {
                    if (strtolower($company['estimatetitle']) == "estimate") {
                        $logoPath = IMAGE . "/newimages/estimate.png";
                        $logoURL = $logoPath;
                    }
                }
            }
        }

        // Generate the logo image HTML
        $logoSrc = "";
        if (!empty($logoPath)) {
            $logoSrc = $logoPath;
        }
    // END Determine logo path and URL

    // Settings
        $settings = $invoiceController->query(
            "SELECT LOWER(printscanresults) AS printScanResults,LOWER(showpromisedateoninvoice) as showPromiseDateOnInvoice FROM settings WHERE shopid = ?",
            [
                ['s', $shopid]
            ]
        );
        $printscanresults = $settings["printScanResults"];
        $showpromisedateoninvoice = $settings["showPromiseDateOnInvoice"];
    // END Settings

    // Michigan Mechanic License
    $showmichmechanic = "no";
    if (strtolower($company["CompanyState"]) == "mi" || strtolower($company["CompanyState"]) == "michigan") {
        $showmichmechanic = "yes";
    }

    // Florida Invoice Redirection
    if (strtolower($company["CompanyState"]) == "fl" || strtolower($company["CompanyState"]) == "florida") {
        redirect_to("../florida/printpdfro.php?shopid=" . $shopid . "&roid=" . $roid);
    }

    // Fax set as phone
    $shopphone = formatphone($company["CompanyPhone"]);
    if (!empty($company["CompanyFax"])) {
        if ($shopid == "3882") {
            $shopphone .= "  Cell: " . formatphone($company["CompanyFax"]);
        } else {
            $shopphone .= "  Fax: " . formatphone($company["CompanyFax"]);
        }
    }

    // Storage Fee
    $storagefee = is_numeric(trim($company['StorageFee'])) 
        ? number_format($company['StorageFee'], 2)
        : $company['StorageFee'];

    // EPA
    $shopepa = "";
    if (strlen($company["EPANo"]) > 1) {
        $shopepa = "EPA# " . $company["EPANo"];
    }

    // Bar Number
        if (strlen($company["BarNo"]) > 1) {
            if (strtoupper(left($company["BarNo"], 3)) == "GST" || 
                strtoupper(left($company["BarNo"], 3)) == "HST" || 
                strtoupper(left($company["BarNo"], 3)) == "PST" || 
                strtoupper(left($company["BarNo"], 3)) == "QST") {
                $shopbar = $company["BarNo"];
            } else {
                $shopbar = "BAR# " . $company["BarNo"];
            }
        } else {
            $shopbar = "";
        }
    // END Bar Number

    // Canadian Tax
        $canadiantax = $ro["canadiantax"];
        $hst = 0;
        $pst = 0;
        $gst = 0;
        $qst = 0;
        if (!empty($canadiantax)) {
            $cantar = explode(",", $canadiantax);
            $hst = $cantar[0];
            $pst = $cantar[1];
            $gst = $cantar[2];
            $qst = $cantar[3];
        }

        $sepCantaxCharged = [];
        $cantaxLabels = [];

        if ($company['chargehst'] === "yes" && $hst > 0) {
            $sepCantaxCharged['hst'] = 'HST';
            $cantaxLabels[] = 'HST';
        }

        if ($company['chargegst'] === "yes" && $gst > 0) {
            $sepCantaxCharged['gst'] = 'GST';
            $cantaxLabels[] = 'GST';
        }

        if ($company['chargepst'] === "yes" && $pst > 0) {
            $sepCantaxCharged['pst'] = 'PST';
            $cantaxLabels[] = 'PST';
        }

        if ($company['chargeqst'] === "yes" && $qst > 0) {
            $sepCantaxCharged['qst'] = 'QST';
            $cantaxLabels[] = 'QST';
        }

        $cantaxstr = implode('+', $cantaxLabels);

        $showCanTax = "";

        if (!empty($cantaxstr) && !$showSeparateCanadianTaxes) {
            $showCanTax = '<tr><td style="width: 60%; text-align: left;">' . $cantaxstr . ' @ ' . $ro['TaxRate'] . '%</td><td style="width: 35%; text-align: right;">' . asDollars($ro['SalesTax']) . '</td></tr>';
        }

        elseif ($showSeparateCanadianTaxes) {      
            $cantaxRow = $invoiceController->query(
                "SELECT * FROM canadiantaxcharged WHERE shopid = ? AND roid = ?",
                [
                    ['s', $shopid],
                    ['i', $roid]
                ]
            );
            
            foreach ($sepCantaxCharged as $key => $label) {
                $rateKey = $key . "rate";
                $amountKey = $key . "amount";

                if (!empty($cantaxRow[$rateKey]) || !empty($cantaxRow[$amountKey])) {
                    $rate = $cantaxRow[$rateKey];
                    $amount = $cantaxRow[$amountKey];
                    $showCanTax .= '<tr><td style="width: 60%; text-align: left;">' . $label . ' @ ' . $rate . '%</td><td style="width: 35%; text-align: right;">' . asDollars($amount) . '</td></tr>';
                }
            }
        }
    // END Canadian Tax

    // Surcharging
        $surcharge = $invoiceController->query(
            "SELECT COALESCE(sum(`amt`), 0) AS payments,COALESCE(sum(`surcharge`), 0) AS surcharge FROM accountpayments WHERE shopid = ? AND roid = ?",
            [
                ['s', $shopid],
                ['i', $roid]
            ]
        );
        $otherpayments = $surcharge['payments'];
        $surchargepayments = $surcharge['surcharge'];
        
        

        $surcharge = $invoiceController->query(
            "SELECT surchargemsg FROM accountpayments WHERE shopid = ? AND roid = ? AND surchargemsg IS NOT NULL AND surchargemsg != '' LIMIT 1",
            [
                ['s', $shopid],
                ['i', $roid]
            ]
        );
        $surchargemessage = !empty($surchargepayments)
            ? " *" . strtoupper($surcharge['surchargemsg'])
            : '';
    // END Surcharging

    // Writer
    $writer = strtolower($company["showadvisoroninvoice"]) == "yes"
        ? "Writer: " . strtoupper($ro["Writer"])
        : "";

    // Source
    $custsource = strtolower($company["showsourceonprintedro"]) == "yes"
        ? $ro["source"]
        : "";

    // PO Number
    $ponumber = !empty($ro["ponumber"])
        ? " PO Number: " . $ro["ponumber"]
        : "";

    // Recommended Repair
        $recommendedRepairs = $invoiceController->query(
            "SELECT UCASE(`desc`) as descr, totalrec FROM recommend WHERE shopid = ? AND roid = ?",
            [
                ['s', $shopid],
                ['i', $roid]
            ],
            false
        );

        $recommendedRepairsStr = '';

        if (is_array($recommendedRepairs)) {
            $count = count($recommendedRepairs);
            foreach ($recommendedRepairs as $i => $repair) {
                $recommendedRepairsStr .= $repair['descr'] . " ( $" . number_format($repair['totalrec'], 0) . ")";
                if ($i < $count - 1) {
                    $recommendedRepairsStr .= " <br>";
                }
            }
        }
    // END Recommended Repair

    // Vehicle Labels
        $vehicleLabel = $invoiceController->query(
            "SELECT UCASE(yearlabel) AS yearlabel, UCASE(makelabel) AS makelabel, UCASE(modellabel) AS modellabel, UCASE(vinlabel) AS vinlabel, UCASE(enginelabel) AS enginelabel, UCASE(translabel) AS translabel, UCASE(drivelabel) AS drivelabel, UCASE(licenselabel) AS licenselabel, UCASE(fleetlabel) AS fleetlabel, UCASE(statelabel) AS statelabel, UCASE(colorlabel) AS colorlabel FROM vehiclelabels WHERE shopid = ?",
            [
                ['s', $shopid]
            ]
        );
    // END Vehicle Labels

    // RO ID
        $printtype = ($ro['status'] == "final" || strtolower($ro['status']) == "closed")
            ? $company['invoicetitle']
            : $company['estimatetitle'];

        $roidPrint = $printtype . ' # ' . $printroid;
        $length = strlen($roidPrint);

        if ($length > 22) {
            $roidfs = "medium";
        } elseif ($length > 15) {
            $roidfs = "large";
        } else {
            $roidfs = "x-large";
        }
    // END RO ID

    // Customer Info Section
        $customer = trim(str_replace("&#39;", "'", strtoupper(substr($ro["Customer"], 0, 32))));
        $customerAddress = strtoupper($ro["CustomerAddress"]);

        $csz = array_filter([strtoupper($ro["customercity"] ?? ''), strtoupper($ro["customerstate"] ?? '')]);
        $customerCSZ = implode(', ', $csz);
        $customerCSZ .= !empty($ro["customerzip"]) ? (empty($csz) ? '' : '. ') . $ro["customerzip"] : '';

        if ($remoteauth == "yes") {
            $customerAddress = "  Not Shown for Security";
            $customerCSZ = "  Not Shown for Security";
        }

        $customerPhoneLines = [];

        if (!empty($ro["CustomerPhone"])) {
            $customerPhoneLines[] = "Home: " . formatphone($ro["CustomerPhone"]);
        }
        if (!empty($ro["CellPhone"])) {
            $customerPhoneLines[] = "Cell: " . formatphone($ro["CellPhone"]);
        }
        if (!empty($ro["CustomerWork"])) {
            $customerPhoneLines[] = "Work: " . formatphone($ro["CustomerWork"]);
        }
        
        $spousecell = "";
        $spousework = "";
        $customertype = "";

        $customerData = $invoiceController->query(
            "SELECT spousecell, spousework, UCASE(customertype) as customertype  FROM customer WHERE shopid = ? AND customerid = ?",
            [
                ['s', $shopid],
                ['i', $ro['customerid']]
            ]
        );
        $spousecell = $customerData['spousecell'] ?? '';
        $spousework = $customerData['spousework'] ?? '';
        $customertype = $customerData['customertype'] ?? '';

        if ( $customertype == "NET 10" || $customertype == "NET 15" || $customertype == "NET 30" || $customertype == "NET 60" || $customertype == "NET 90" ){
            $customertype = "({$customertype})";
        }else{
            $customertype = "";
        }
        
        if (!empty($spousecell)) {
            $customerPhoneLines[] = "Spouse: " . formatphone($spousecell);
        }

        $customerPhones = implode('<br>', $customerPhoneLines);

        $customerDetails = '';
        $customerDetailsRowCount = -3; // By default 4 is ok
        if (!empty($customer)){
            $customerDetails .= "<b>$customer</b><br>";
            $customerDetailsRowCount++;
        }
        if (!empty($customerAddress)){
            $customerDetails .= "$customerAddress<br>";
            $customerDetailsRowCount++;
        }
        if (!empty($customerCSZ)){
            $customerDetails .= "$customerCSZ<br>";
            $customerDetailsRowCount++;
        }
        if (!empty($customerPhones)){
            $customerDetails .= "$customerPhones";
            $customerDetailsRowCount += count($customerPhoneLines) - 1;
        }

        // Customer Info Section -> Vehicle Details
        $transmissionDrive = !empty($ro["VehTrans"])
            ? $ro["VehTrans"] . (!empty($ro["DriveType"]) ? ' | ' . $ro["DriveType"] : '')
            : $ro["DriveType"];


        $vehicleMakeModel = substr(
            strtoupper(str_replace("&#39;", "'", $ro["vehmake"] . " " . $ro["vehmodel"])),
            0,
            22
        );

        $vehicleLicenseState = !empty($ro["vehlicense"])
            ? $ro["vehlicense"] . (!empty($ro["vehstate"]) ? ' | ' . $ro["vehstate"] : '')
            : $ro["vehstate"];
    // END Customer Info Section
    
    // IN N OUR Dates
        $lineParts = [];

        // Basic dates
        $lineParts[] = "IN: " . date('n/j/Y', strtotime($ro["DateIn"]));
        $lineParts[] = "OUT: " . date('n/j/Y', strtotime($ro["StatusDate"]));

        // Promise date
        if ($showpromisedateoninvoice === 'yes' && !empty($ro["datetimepromised"])) {
            $lineParts[] = "PD: " . date('n/j/Y g:i A', strtotime($ro["datetimepromised"]));
        }

        // PO number
        if (!empty($ponumber)) {
            $lineParts[] = strtoupper($ponumber);
        }

        // Custom vehicle labels
        for ($i = 1; $i <= 4; $i++) {
            $label = strtoupper($ro["customvehicle{$i}label"]) ?? '';
            $value = strtoupper($ro["customvehicle{$i}"]) ?? '';
            if (!empty($label)) {
                $lineParts[] = "$label $value";
            }
        }

        // Source
        if (!empty($ro['source']) && $company['showsourceonprintedro'] === "yes") {
            $lineParts[] = "SOURCE: " . strtoupper($ro['source']);
        }

        // Email
        if (!empty($ro['email'])) {
            $lineParts[] = "EMAIL: " . $ro['email'];
        }

        // Final line
        $lineval = implode('  ', $lineParts);
    // END IN N OUR Dates

    // Tire Pressure
    $carImage = IMAGE . "/newimages/gencarsmall.jpg";


    // Get Complaints
        $complaintsParams = [
            ['s', $shopid],
            ['i', $roid]
        ];

        $hideDeclined = "";
        if ($company['showdeclined'] !== 'yes') {
            $hideDeclined = " AND acceptdecline != ?";
            $complaintsParams[] = ['s', 'Declined'];
        }

        $complaints = $invoiceController->query(
            "SELECT complaint, advisorcomments, complaintid, acceptdecline, techreport, scanreport FROM complaints WHERE cstatus = 'no' AND shopid = ? AND roid = ? $hideDeclined GROUP BY complaintid ORDER BY displayorder",
            $complaintsParams,
            false
        );

        $showPrices = strtolower($company['nexpartusername']) == 'yes';
        foreach ($complaints as &$complaint) {
            $techReport = iconv('UTF-8', 'ASCII//TRANSLIT', $complaint["techreport"]);
            $techReport = html_entity_decode($techReport);
            $techReport = str_replace(["&#34;", "&#39;"], ["''", "'"], $techReport);
            $complaint["techreport"] = $techReport;
            unset($techReport);

            $complaintWOX = iconv('UTF-8', 'ASCII//TRANSLIT', $complaint["complaint"]);
            $complaintWOX = html_entity_decode($complaintWOX);
            $complaintWOX = str_replace(["&#34;", "&#39;"], ["''", "'"], $complaintWOX);
            $complaint["complaint"] = $complaintWOX . (strtolower($complaint['acceptdecline']) == 'declined' ? " (CUSTOMER DECLINED)" : '');
            
            $advisorComment = iconv('UTF-8', 'ASCII//TRANSLIT', $complaint["advisorcomments"]);
            $advisorComment = html_entity_decode($advisorComment);
            $advisorComment = str_replace(["&#34;", "&#39;"], ["''", "'"], $advisorComment);
            $complaint["advisorcomments"] = $advisorComment;
            unset($advisorComment);

            $scanReport = str_replace(["&#34;", "&#39;"], ["''", "'"], $complaint['scanreport']);
            $complaint["scanreport"] = nl2br($scanReport);
            unset($scanReport);

            $complaint['totalPrice'] = 0;

            // Parts
                $partsQuery = strtolower($complaint['acceptdecline']) == 'declined'
                    ? "SELECT partnumber, partcode, partprice, linettlprice, partdesc, quantity, discount FROM recommendparts WHERE shopid = ? AND roid = ? AND complaintid = ?"
                    : "SELECT partnumber, partcode, partprice, linettlprice, partdesc, quantity, discount FROM parts WHERE shopid = ? AND roid = ? AND complaintid = ? AND deleted != 'yes' ORDER BY displayorder, partid";
                
                $complaint['parts'] = $invoiceController->query(
                    $partsQuery,                    
                    [
                        ['s', $shopid],
                        ['i', $roid],
                        ['i', $complaint['complaintid']]
                    ],
                    false
                );

                foreach ($complaint['parts'] as &$part) {
                    $part['partdesc'] = iconv('UTF-8', 'ASCII//TRANSLIT', $part['partdesc']);
                    $part['partdesc'] = html_entity_decode($part['partdesc']);
                    $part['partdesc'] = str_replace(["&#34;", "&#39;"], ["''", "'"], $part['partdesc']);
                    $part['partnumber'] = trim($part['partnumber']) === "JOB"
                        ? "JOB"
                        : (
                            $company['showpartnumberonprintedro'] === "yes"
                                ? "PART: " . strtoupper($part['partnumber'])
                                : "PART"
                        );
                    
                    $part['quantityDisplay'] = "";
                    $part['discountDislay'] = "";

                    $discountDisplay = ($company['partsdiscountonro'] == "yes" && doubleval($part["discount"]) > 0)
                        ? "({$part['discount']}% Discount)"
                        : "";

                    if ( strtolower($complaint['acceptdecline']) != 'declined' ){
                        if ( $showPrices ){
                            $part['quantityDisplay'] = $part['quantity'] . " @ " . asDollars($part['partprice']);
                            $part['discountDislay']= $discountDisplay . asDollars($part['linettlprice']);
                        }else{
                            $part['quantityDisplay'] = $part['quantity'];
                        }
                    }
                    $complaint['totalPrice'] += $part['linettlprice'];
                }

                unset($part);
            // END Parts

            // Labor
                $laborQuery = strtolower($complaint['acceptdecline']) == 'declined'
                    ? "SELECT `desc` labor, total linetotal, hours laborhours, rate hourlyrate, 0 AS discount, tech FROM recommendlabor WHERE shopid = ? AND roid = ? AND comid = ?"
                    : "SELECT tech, linetotal, laborhours, hourlyrate, discount, labor FROM labor WHERE shopid = ? AND roid = ? AND complaintid = ?";

                $complaint['labors'] = $invoiceController->query(
                    $laborQuery,                    
                    [
                        ['s', $shopid],
                        ['i', $roid],
                        ['i', $complaint['complaintid']]
                    ],
                    false
                );

                foreach($complaint['labors'] as &$labor){
                    $employee = explode(',', $labor['tech']);
                    $employeeFirst = trim($employee[0]);
                    $employeeLast = trim($employee[1]);
                    $labor['labor'] = str_replace(["&#34;", "&#39;"], ["''", "'"], $labor['labor']);
                    
                    $labor['mechanicNumber'] = ($invoiceController->query(
                        "SELECT mechanicnumber FROM employees WHERE shopid = ? AND employeelast = ? AND employeefirst = ? AND showtechlist = 'yes' LIMIT 1",
                        [
                            ['s', $shopid],
                            ['s', $employeeFirst],
                            ['s', $employeeLast]
                        ]
                    ))['mechanicnumber'];

                    $labor['hoursDisplay'] = (
                        $company['showlaborhoursonro'] === "yes" && 
                        strtolower($complaint['acceptdecline']) !== 'declined'
                    ) ? $labor["laborhours"] . " @ " . asDollars($labor["hourlyrate"]) : "";

                    $labor['lineTotalDisplay'] = (
                        $showPrices && 
                        strtolower($complaint['acceptdecline']) !== 'declined'
                    ) ? (
                        doubleval($labor["discount"])
                            ? "(Discount: " . asDollars($labor["discount"]) . ") " . asDollars($labor["linetotal"])
                            : asDollars($labor["linetotal"])
                    ) : "";

                    $complaint['totalPrice'] += $labor['linetotal'];
                }

                unset($labor);
            // END Labor

            // Sublet
                $subletQuery = strtolower($complaint['acceptdecline']) == 'declined'
                    ? "SELECT subletdesc, subletprice FROM recommendsublet WHERE shopid = ? AND roid = ? AND comid = ?"
                    : "SELECT subletdesc, subletprice FROM sublet WHERE shopid = ? AND roid = ? AND complaintid = ?";
                
                $complaint['sublets'] = $invoiceController->query(
                    $subletQuery,                    
                    [
                        ['s', $shopid],
                        ['i', $roid],
                        ['i', $complaint['complaintid']]
                    ],
                    false
                );

                foreach ($complaint['sublets'] as &$sublet) {
                    $sublet['subletdesc'] = str_replace(["&#34;", "&#39;"], ["''", "'"], $sublet['subletdesc']);

                    $complaint['totalPrice'] += $sublet['subletprice'];
                }
                unset($sublet);
            // END Sublet
        }
        unset($complaint);
    // END Get Complaints 

    // Revisions
        $revisions = array();
        $totalRevision = 0;
        if ( strtolower($company['showrevapps']) == "yes" ){
            $revisions = $invoiceController->query(
                "SELECT revamt, revby, revphone, revdate, revtime, revappmethod, revappby FROM revisions WHERE shopid = ? and roid = ?",
                [['s', $shopid], ['i', $roid]],
                false
            );

            foreach ($revisions as $revision) {
                $totalRevision += $revision['revamt'];
            }
        }
    // END Revisions

    // Recall Option
        $recall = ($invoiceController->query(
            "SELECT showrecall FROM settings WHERE shopid = ? LIMIT 1",
            [['s', $shopid]],
        ))['showrecall'];

        $showRecall = !empty($showrecall) && strtolower($showrecall) == 'yes';
    // END Recall Option

    // Payments
        $payments = $invoiceController->query(
            "SELECT pnumber, ptype, pnumber, last4, pdate, amt, surcharge FROM accountpayments WHERE shopid = ? AND roid = ?",
            [['s', $shopid], ['i', $roid]],
            false
        );

        foreach ($payments as &$payment){
            $apprcode = "";
            $ctype = $payment["ptype"];
            $ref = "";

            $pnumber = $payment["pnumber"];
            if (left($pnumber, 3) === "360" && strpos($pnumber, "~") !== false) {
                $payar = explode("~", $pnumber);
                $apprcode = end($payar);
                $check360 = $payar[0];

                switch ($check360) {
                    case "360SYNC":
                        $ctype = "Synchrony";
                        break;
                    case "360ECP":
                        $ctype = "EasyPay";
                        break;
                    case "360WISE":
                        $ctype = "Wisetack";
                        break;
                    case "360AFF":
                        $ctype = "AFF";
                        break;
                }
            } elseif (!empty($pnumber)) {
                $ref = "REF # " . $pnumber;
            }

            if (!empty($apprcode)) {
                $ctype .= " - AuthCode " . $apprcode;
            }

            if (!empty($payrs["last4"])) {
                $ctype .= " - CC# " . $payrs["last4"];
            }

            $payment['paymentType'] = $ctype;
            $payment['reference'] = $ref;
            $payment['paymentDate'] = date('n/j/Y', strtotime($payment['pdate']));
            $payment['paymentAmount'] = asDollars($payment['amt'] + $payment['surcharge']) . (!empty($payment['surcharge']) ? '*' : '');
        }
        unset($payment);
    // END Payments

    // Print Comm Log
        $commLogs = $invoiceController->query(
            "SELECT `datetime`,UCASE(`by`) as byy, UCASE(comm) as comm FROM repairordercommhistory WHERE shopid = ? AND roid = ? AND comm!='Tech Story Updated' AND comm!='Advisor Comments Updated'",
            [['s', $shopid],['i', $roid]],
            false
        );        
    // END

    // Footer
        $jobTotal = ($invoiceController->query(
            "SELECT coalesce(sum(linettlprice),0) ttljob FROM parts WHERE shopid = ? AND roid = ? AND deleted != 'yes' AND partnumber = 'JOB'",
            [['s', $shopid],['i', $roid]]
        ))['ttljob']; 

        $roDisclosure = $ro['rodisc'] > 10
            ? nl2br($ro['rodisc'])
            : nl2br($company['rodisclosure']);

        $warrantyDisclosure = $ro['warrdisc'] > 10
            ? nl2br($ro['warrdisc'])
            : nl2br($company['rowarrdisclosure']);
    // END Footer
?>
