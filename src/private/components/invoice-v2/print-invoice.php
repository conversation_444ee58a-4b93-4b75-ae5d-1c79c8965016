<?php 
    ob_start();
    require_once COMPONENTS_PRIVATE_PATH . "/invoice-v2/invoice.php";
    include INTEGRATIONS_PATH . "/TCPDF/tcpdf.php";
    include INTEGRATIONS_PATH . "/FPDF/fpdf.php";
    include INTEGRATIONS_PATH . "/FPDF/FPDI-2.3.4/src/autoload.php";

    $invoicePreferences = [
        'companyLogo' => [
            'align' => $_GET['companyLogoAlign'] ?? $invoice['company_logo_align'] ?? 'center',
            'show' => $_GET['showCompanyLogo'] ?? $invoice['show_company_logo'] ?? true,
            'size' => $_GET['companyLogoSize'] ?? $invoice['company_logo_size'] ?? '90px',
        ],
        'font' => [
            'header' => $_GET['headerFontSize'] ?? $invoice['font_header'] ?? '11px',
            'title' => $_GET['titleFontSize'] ?? $invoice['font_title'] ?? '18px',
            'body' => $_GET['bodyFontSize'] ?? $invoice['font_body'] ?? '10px',
            'footer' => $_GET['footerFontSize'] ?? $invoice['font_footer'] ?? '9px',
            'disclosure' => $_GET['disclosureFontSize'] ?? $invoice['font_disclosure'] ?? '10px',
        ],
        'color' => [
            'body' => $_GET['bodyColor'] ?? $invoice['color_body'] ?? '#262626',
            'title' => $_GET['invoiceTitleColor'] ?? $invoice['color_title'] ?? '#262626',
            'issue' => [
                'color' => $_GET['tableHeaderColor'] ?? $invoice['color_issue_text'] ?? '#ffffff',
                'bg' => $_GET['tableHeaderBgColor'] ?? $invoice['color_issue_bg'] ?? '#262626',
            ],
        ],
        'qr' => $_GET['qr'] ?? $invoice['qr'] ?? '',
        'whatermark' => $_GET['whatermark'] ?? $invoice['whatermark'] ?? false,
        'newPageForDisclosure' => $_GET['newPageForDisclosure'] ?? $invoice['new_page_for_disclosure'] ?? false,
        'signatureDisclosure' => $_GET['signatureDisclosure'] ?? $roDisclosure,
        'warrantyDisclosure' => $_GET['warrantyDisclosure'] ?? $warrantyDisclosure,
        'headerSpace' => $_GET['headerSpace'] ?? $invoice['header_space'] ?? 0,
        'footerSpace' => $_GET['footerSpace'] ?? $invoice['footer_space'] ?? 0,
    ];

    $logoAlign = $invoicePreferences['companyLogo']['align'];
    $showLogo = ($invoicePreferences['companyLogo']['show'] == 'true') || ($invoicePreferences['companyLogo']['show'] === 1);
    $logoSize = $invoicePreferences['companyLogo']['size'];

    $leftColAlign = $logoAlign === 'left' ? 'center' : 'left';
    $rightColAlign = $logoAlign === 'right' ? 'center' : 'right';

    $headerMargin = 180 + $invoicePreferences['headerSpace'];
    $headerMargin += $customerDetailsRowCount > 0 
        ? $customerDetailsRowCount * 8
        : 0; 
    $footerMargin = 160 + $invoicePreferences['footerSpace'];
    $qrSize = 50;

    if ( $invoicePreferences['qr'] ){
        $footerMargin += 55;
    }

    $footerMargin += !empty($surchargepayments) && 10;

    class MYPDF extends \setasign\Fpdi\Tcpdf\Fpdi
    {
        public $footerHTML = "";

        // Page header
        public function Header()
        {
            global $logoURL, $invoicePreferences;
            
            $headerData = $this->getHeaderData();
            $this->writeHTML($headerData['string']);

            if ( $invoicePreferences['whatermark'] === 'true' ) {
                $imgWidth = 200;
                $imgHeight = 0;

                $pageWidth = $this->getPageWidth();
                $pageHeight = $this->getPageHeight();

                $x = ($pageWidth - $imgWidth) / 2;
                $y = ($pageHeight - $imgWidth) / 2;

                $this->SetAlpha(0.2);

                $this->Image($logoURL, $x, $y, $imgWidth, $imgHeight, '', '', '', false, 300);

                $this->SetAlpha(1); 
            }     
        }

        // Page footer
        public function Footer()
        {
            global $invoicePreferences, $footerMargin, $qrSize;

            $hex = ltrim($invoicePreferences['color']['body'], '#');
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));

            $this->SetTextColor($r, $g, $b);

            if ($invoicePreferences['qr']) {
                $qrUrl = "https://quickchart.io/qr?text=" . urlencode($invoicePreferences['qr']) . "&size=40";
                $this->Image(
                    $qrUrl,
                    15,
                    $this->getPageHeight() - $footerMargin,
                    $qrSize,
                    $qrSize
                );

                $this->ln($qrSize + 5);
            }

            $this->writeHTML($this->footerHTML);
            $this->SetFont('helvetica', 'B', 8);
            $this->SetXY($this->getPageWidth() - 78, $this->getPageHeight() - 20);

            $pageNumTBL = '<table style="width: 100pt"><tr><td>PAGE ' . $this->getAliasNumPage() . ' OF ' . $this->getAliasNbPages() . '</td></tr></table>';
            $this->writeHTML($pageNumTBL, true, false, true, false, 'R');
        }
    }

    $newPageForDisclosure = $invoicePreferences['newPageForDisclosure'] == "true" ||  $invoicePreferences['newPageForDisclosure'] === 1 || $invoicePreferences['newPageForDisclosure'] === true;
    $footerMargin -= $newPageForDisclosure 
        ? 60
        : 0; 
    // Create PDF instance with custom size (612x780 points)
    $pdf = new MYPDF(PDF_PAGE_ORIENTATION, 'px', array(612, 780), true, 'UTF-8', false);

    // Load header HTML from external file
    ob_start();
    include 'sections/header.php';
    $header = ob_get_clean();

    ob_start();
    include 'sections/main.php';
    $main = ob_get_clean();

    ob_start();
    include 'sections/footer.php';
    $footer = ob_get_clean();

    // Assign header content
    $pdf->setHeaderData('', 0, '', $header, array(0, 0, 0), array(0, 0, 0));
    $pdf->footerHTML = $footer;

    // Header and Footer settings
    $pdf->setPrintHeader(true);
    $pdf->setPrintFooter(true);

    // Font and Margin settings
    $pdf->SetMargins(20, $headerMargin, 20);
    $pdf->SetHeaderMargin(20);
    $pdf->SetFooterMargin($footerMargin);
    $pdf->SetAutoPageBreak(true, $footerMargin + 10);

    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

    // Add a new page
    $pdf->AddPage();

    // Optionally write content here
    $pdf->writeHTML($main, true, false, true, false, '');

    $pdf->endPage();

    if ($newPageForDisclosure && isset($shopid)) {
        $pdf->setPrintHeader(false);
        $pdf->SetMargins(20, 20, 20);
        $pdf->setPrintFooter(false);

        $disclosurePath = PDFINVOICES_PATH . "/custom-disclosure/" . $shopid . '/disclosure.pdf';
        
        if (file_exists($disclosurePath)) {
            $pageCount = $pdf->setSourceFile($disclosurePath);

            for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                $pdf->AddPage();
                $tplIdx = $pdf->importPage($pageNo);
                $pdf->useTemplate($tplIdx);
                
                // Add signature on the last page only
                if ($pageNo == $pageCount) {
                    // Disable auto page break temporarily
                    $pdf->SetAutoPageBreak(false);

                    $pageHeight = $pdf->getPageHeight();
                    $signatureY = $pageHeight - 100; // Try a safer distance from bottom
                    $signatureStartX = 50;

                    $pdf->SetFont('helvetica', '', 10);
                    $pdf->SetXY($signatureStartX, $signatureY);
                    $pdf->Cell(0, 10, 'X: _____________________________________ DATE: ' . date('n/j/Y'), 0, 0, 'L');

                    // Re-enable auto page break
                    $pdf->SetAutoPageBreak(true, 20);
                }
            }
        }
    }

    ob_end_clean();

    // Output PDF inline to browser
    $pdf->Output('example.pdf', 'I');
?>
